<template>
  <div class="chat">
    <TUIKit class="chat-container">
      <TUIChat :toAccount="toAccount"> </TUIChat>
    </TUIKit>
    <ChatAiToolBar v-if="chatStore.showAiToolBar" @chooseTool="chooseTool" />
    <!-- <TUICallKit class="callkit-container" :allowedMinimized="true" :allowedFullScreen="false" /> -->
    <IMImageCutDiagram v-if="chatStore.aiCutoutsShow" :src="chatStore.aiCutoutsSrc"
      @save="file => handleSaveSplit(file)" />
    <!-- ImageEditor 组件集成 -->
    <ImageEditor />
  </div>
</template>
<script lang="ts" setup>
import { TUIKit, TUIChat } from '@/components/TUIKit'
import TUIChatEngine, { TUIConversationService, TUIChatService, TUIUserService, TUIStore, StoreName } from '@tencentcloud/chat-uikit-engine'
import { onMounted, provide, computed, onBeforeUnmount, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { clearAllMessage } from '@/api/aiChat'
import { setMessage } from '@/api/common'
import useUserStore from '../../store/modules/user'
import useChatStore from '../../store/modules/chat'
import { ElMessage } from 'element-plus'
import IMImageCutDiagram from '@/components/IMImageCutDiagram/index.vue'
import ChatAiToolBar from '@/components/ChatAiToolBar/index.vue'
import ImageEditor from '@/components/ImageEditor/index.vue'
import { blobToBase64, ossClient, resizeBase64Img } from '../TUIKit/utils/tool'
import { clearIMHistory, sendCustomMessage } from '@/utils/customIM'
const props = defineProps({
  toAccount: String,
  type: String,
  userId: String,
})
const { toAccount, type, userId } = props
const userStore = useUserStore()
const chatStore = useChatStore()
// const { setSendStatus } = chatStore
const route = useRoute()


const roleType: { [key: string]: string } = {
  student: 'user',
  teacher: 'teacher',
}

TUIStore.update(StoreName.APP, 'enableTyping', true)
onMounted(() => {
  switchConversation()
})

const chooseTool = (item: any) => {
  // chatStore.setShowAiVideo(true)
  console.log('item', item)
  chatStore.setTool('')
  nextTick(() => {
    chatStore.setTool(item)
  })
}
const handleSaveSplit = async (file: File) => {
  const url = await ossClient().upload(file)
  const base64 = await blobToBase64(file)
  const img = await resizeBase64Img(base64, 1000, false)
  sendCustomMessage({
    data: {
      businessID: 'ai_custom_msg',
      content: {
        name: 'image',
        data: [
          {
            url,
            width: img?.width,
            height: img?.height,
          },
        ],
      },
    },
  })
}
function switchConversation() {
  console.log('尝试切换会话，toAccount:', toAccount)
  if (!toAccount) {
    console.warn('未提供有效的toAccount，无法切换会话')
    return
  }

  let conversationID: string
  if (type === 'c2c') {
    conversationID = `C2Cagent-${toAccount}`
  } else if (type === 'c2group') {
    conversationID = toAccount.startsWith('GROUP') ? toAccount : `GROUP${toAccount}`
  } else {
    console.error('未知的会话类型:', type)
    return
  }

  console.log('切换会话到:', conversationID)

  // 设置最大重试次数和当前重试次数
  const MAX_RETRY_COUNT = 5
  let retryCount = 0

  // 定义定时器ID
  let checkConversationTimer: ReturnType<typeof setTimeout> | null = null

  // 切换会话的函数
  const checkAndSwitchConversation = () => {
    // 获取当前会话列表
    const conversationList = TUIStore.getData(StoreName.CONV, 'conversationList') || []
    console.log('当前会话列表状态:', conversationList.length ? '已加载' : '未加载', '会话数量:', conversationList.length)

    // 尝试切换会话
    TUIConversationService.switchConversation(conversationID)
      .then((res) => {
        console.log('会话切换成功:', conversationID, res)
        // 清除定时器
        if (checkConversationTimer) {
          clearTimeout(checkConversationTimer)
          checkConversationTimer = null
        }
        // 根据不同路由发送不同的初始消息
        sendInitialMessage()
        // 设置消息阅读状态
        TUIUserService.switchMessageReadStatus(false)
        clearIMHistory()
      })
      .catch(err => {
        console.error(`会话切换失败 (尝试 ${retryCount + 1}/${MAX_RETRY_COUNT}):`, err, 'conversationID:', conversationID)

        // 增加重试计数
        retryCount++

        // 如果已达到最大重试次数，则停止尝试
        if (retryCount >= MAX_RETRY_COUNT) {
          console.warn(`已达最大重试次数(${MAX_RETRY_COUNT})，停止尝试切换会话`)
          return
        }

        // 否则，延迟一段时间后再次尝试
        checkConversationTimer = setTimeout(checkAndSwitchConversation, 2000)
      })
  }

  // 初始尝试切换
  checkAndSwitchConversation()
}
const isTeacher = computed(() => {
  return route.query.role === 'teacher'
})

// 定义消息配置类型
interface MessageConfig {
  businessID: string
  content: {
    name: string
    data: Record<string, any>
  }
}

// 提取发送初始消息的逻辑到单独函数
async function sendInitialMessage() {
  // 定义消息配置
  let messageConfig: MessageConfig | null = null

  if (route.path.includes('/application/bot')) {
    messageConfig = {
      businessID: 'ai_say',
      content: {
        name: 'sayhello',
        data: {},
      },
    }
  } else if (route.path.includes('/ai-chat')) {
    // 检查 courseid 是否为字符串或数字
    const courseid = route?.query?.courseid
    const isValidType = typeof courseid === 'string' || typeof courseid === 'number' || (courseid !== undefined && !isNaN(Number(courseid)))

    if (!isValidType) {
      ElMessage({
        message: 'courseid 参数类型错误，请提供有效的字符串或数字',
        type: 'error',
        duration: 0,
        showClose: true,
      })
      // return // 不发送消息但不关闭窗口
    }

    messageConfig = {
      businessID: 'ai_say',
      content: {
        name: 'sayhello',
        data: {},
      },
    }
  }
  // return
  // 如果没有匹配的路由，不发送消息
  if (!messageConfig) return

  try {
    if (isTeacher.value) {
      // 确定会话类型和接收者
      const conversationType = type === 'c2c' ? TUIChatEngine.TYPES.CONV_C2C : TUIChatEngine.TYPES.CONV_GROUP
      const to = type === 'c2c' ? `agent-${toAccount}` : toAccount

      await sendCustomMessage(
        {
          data: messageConfig,
        },
        {
          to,
          conversationType,
        }
      )
      console.log(`${route.query?.usertype || route.query?.role}发送${messageConfig.content.name}消息成功`)
    }
  } catch (error) {
    console.error(`发送${messageConfig?.content?.name || '未知'}消息失败:`, error)
  }
}

onBeforeUnmount(() => {
  console.log('onBeforeUnmount')
  // TUIChatEngine.deleteConversation();
  // conversationModel.deleteConversation()
  // TUIStore.getConversationModel(`C2Cagent-${toAccount}`).deleteConversation();
})
// 消息处理逻辑优化
TUIStore.watch(StoreName.CHAT, {
  messageList: (newMessageList: any[]) => {
    console.log('newMessageList', newMessageList)
    // 如果没有消息列表或为空，直接返回
    if (!newMessageList?.length) return

    try {
      // 获取最新消息
      const lastMessage = newMessageList[newMessageList?.length - 1]
      // 判断是否为删除消息的自定义消息
      if (lastMessage.payload.data && JSON.parse(lastMessage.payload.data)?.businessID === 'ai_event' && JSON.parse(lastMessage.payload.data)?.content?.name === 'delete' && !lastMessage.isDeleted) {
        // 删除消息
        const messageID = JSON.parse(lastMessage.payload.data)?.content?.data?.messageID
        //  判断该消息是不是自己发出的
        if (!lastMessage.from.includes(roleType[route.query?.role as string])) {
          // 使用Promise.all处理批量删除
          console.log('all处理批量删除', messageID)
          const messageModel = TUIStore.getMessageModel(lastMessage.ID)
          messageModel?.deleteMessage()

          //  设置删除消息已读
          Promise.all(
            [...messageID].map((id: string) => {
              return new Promise((resolve, reject) => {
                try {
                  const messageModel = TUIStore.getMessageModel(id)
                  messageModel?.deleteMessage()
                  resolve(id)
                } catch (error) {
                  reject({ id, error })
                }
              })
            })
          )
            .then(() => {
              console.log('所有消息删除成功')
            })
            .catch(({ id, error }) => {
              console.error(`消息ID: ${id} 删除失败:`, error)
            })
        }
      }
      // 处理agent消息
      if (isAgentMessage(lastMessage)) {
        // 消息校验
        if (!isValidMessage(lastMessage)) return

        // 解析消息数据
        const messageData = parseMessageData(lastMessage)
        if (!messageData || messageData.status !== 'SUCCESS') return
        handleAgentMessage(lastMessage, messageData)
      }
      // 处理老师消息
      if (
        lastMessage.from.includes('teacher') &&
        roleType[route.query?.role as string] === 'user' &&
        lastMessage.payload.data &&
        JSON.parse(lastMessage.payload.data)?.businessID === 'ai_event' &&
        JSON.parse(lastMessage.payload.data)?.content?.name === 'control'
      ) {
        //清空历史消息
        clearMessage()
        // 发送消息
        sendCustomMessage({
          data: {
            businessID: 'ai_say',
            content: {
              name: 'sayhello',
              data: {},
            },
          },
        })
        console.log('清空消息后发送hello消息成功')
      }
    } catch (error) {
      handleMessageError(error)
    }
  },
})

// 工具函数：检查消息有效性
function isValidMessage(message: any): boolean {
  if (!message?.payload?.data) {
    console.warn('消息数据为空或格式不正确:', message)
    return false
  }
  return true
}

// 工具函数：解析消息数据
function parseMessageData(message: any): any {
  try {
    return JSON.parse(message.payload.data)
  } catch (parseError) {
    console.error('解析消息数据失败:', parseError, message.payload.data)
    return null
  }
}

// 工具函数：判断是否为代理消息
function isAgentMessage(message: any): boolean {
  return !!message.from?.includes('agent')
}

// 处理代理消息
function handleAgentMessage(_message: any, messageData: any): void {
  // 检查是否需要发送提示消息
  if (shouldSendTipsMessage(messageData)) {
    sendTipsMessage()
  }
}

// 判断是否需要发送提示消息
function shouldSendTipsMessage(messageData: any): boolean {
  return (
    (messageData?.replyAccount?.includes(roleType[route?.query?.usertype as string]) || messageData?.replyAccount?.includes(roleType[route.query?.role as string])) &&
    messageData.businessID === 'ai_message' &&
    messageData.text &&
    !messageData?.images?.length &&
    !messageData?.function &&
    !messageData?.video &&
    !messageData?.questionOption &&
    !messageData?.link &&
    !messageData?.tips?.length
  )
}

// 发送提示消息
function sendTipsMessage(): void {
  console.info('发送提示消息sendTipsMessage', new Date().getTime())
  sendCustomMessage({
    data: {
      businessID: 'ai_say',
      content: {
        name: 'tips',
        data: {},
      },
    },
  })
}

// 处理消息错误
function handleMessageError(error: any): void {
  console.error('处理消息时发生错误:', error)
}



TUIStore.watch(StoreName.APP, {
  enableTyping: (enable: any) => {
    console.log('enableTyping updated:', enable)
  },
  typingStatus: (typingStatus: any) => {
    console.log('typingStatus updated:', typingStatus)
  },
})

// 优化清除消息函数
function clearMessage() {
  if (!TUIChatEngine.isReady()) {
    console.log('TUIChatEngine is not ready')
    return
  }

  // 确定会话ID
  const conversationID = props.type === 'c2group' ? `GROUP${toAccount}` : `C2Cagent-${toAccount}`
  // 清除历史消息
  TUIChatService.clearHistoryMessage(conversationID)
    .then(function (_imResponse: any) {
      // 确定来源账号
      const fromAccount = route.path.includes('/ai-chat') ? userId : userStore.userId

      // 构建数据对象
      const data = {
        fromAccount,
        toAccount: `agent-${toAccount}`,
      }

      // 如果是AI聊天路由，则调用clearAllMessage方法
      if (route.path.includes('/ai-chat')) {
        clearAllMessage({ groupId: toAccount })
        // setH5Message(data)
      } else {
        setMessage(data)
      }

      // 重置isCompleted状态，确保"查看更多"按钮不再显示
      TUIStore.update(StoreName.CHAT, 'isCompleted', true)
    })
    .catch(function (imError: any) {
      console.warn('清空消息失败:', imError)
    })
}

provide('clearMessage', clearMessage)
</script>
<style lang="scss">
.chat {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  position: relative;

  .chat-container {
    width: 100%;
    margin: 0 auto;
    height: 100%;
    box-shadow: 0 11px 20px #ccc;
  }
}
</style>
