/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-21 16:39:01
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-29 15:38:05
 * @FilePath: /miaobi-admin-magic-touch/src/utils/customIM.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import TUIChatEngine, { TUIConversationService, TUIChatService, TUIGroupService, TUIUserService, TUITranslateService, TUIStore, StoreName } from '@tencentcloud/chat-uikit-engine'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import type { LocationQueryValue } from 'vue-router'
import useChatStore from '@/store/modules/chat'

const chatStore = useChatStore()
// 获取 URL 参数值的辅助函数（参考 aiChat/index.vue 中的实现）
const getQueryValue = (key: LocationQueryValue | LocationQueryValue[]) => {
  if (Array.isArray(key)) {
    return key[0]
  }
  return key
}

// 发送自定义消息
export async function sendCustomMessage(payload: any, options?: { to?: string; conversationType?: any }): Promise<any> {
  try {
    if (!payload || !payload.data) {
      throw new Error('消息内容不能为空')
    }
    // 直接使用 TUIChatService
    const chatService = TUIChatService
    if (!chatService) {
      throw new Error('聊天服务未初始化')
    }

    // 获取当前路由信息，添加安全检查
    let courseid: string | null = null
    try {
      const urlParams = new URLSearchParams(window.location.search)
      courseid = urlParams.get('courseid')
      console.log('🚀 ~ sendCustomMessage ~ 从URL获取 courseid:', courseid)
    } catch (error) {
      // 如果无法获取路由信息，从 URL 直接解析
      console.log('🚀 ~ sendCustomMessage ~ 路由获取失败，从URL解析:', error)
    }
    // 构建消息数据，如果存在 courseid 则添加到数据中
    const sessionId = chatStore?.sessionId
    const messageData = { ...payload.data }
    if (courseid) {
      messageData.courseid = courseid
      messageData.courseId = courseid
      messageData.sessionId = sessionId
    }

    // 构建自定义消息参数
    const customMessageParams: any = {
      payload: {
        data: JSON.stringify(messageData),
        description: payload.data.businessID || 'ai_custom_msg',
        extension: payload.extension || '',
      },
    }

    // 如果提供了 to 和 conversationType，添加到参数中
    if (options?.to) {
      customMessageParams.to = options.to
    }
    if (options?.conversationType) {
      customMessageParams.conversationType = options.conversationType
    }

    console.log('🚀 ~ sendCustomMessage ~ customMessageParams:', customMessageParams)
    // 发送自定义消息
    const result = await chatService.sendCustomMessage(customMessageParams)
    console.log('自定义消息发送成功:', result)
    return result
  } catch (error: unknown) {
    console.error('发送自定义消息失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '发送消息失败')
    throw error
  }
}

// 发送自定义消息 中止任务
export async function sendCustomMessageStopTask(payload: any): Promise<any> {
  try {
    if (!payload || !payload.data) {
      throw new Error('消息内容不能为空')
    }
    const chatService = TUIChatService
    if (!chatService) {
      throw new Error('聊天服务未初始化')
    }
    const customMessageParams = {
      payload: {
        data: JSON.stringify(payload.data),
      },
    }
    const result = await chatService.sendCustomMessage(customMessageParams)
    console.log('自定义消息发送成功:', result)
    return result
  } catch (error: unknown) {
    console.error('发送自定义消息 中止任务失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '发送消息失败')
    throw error
  }
}
export async function clearIMHistory() {
  const groupId = chatStore?.groupId
  const conversationID = `GROUP${groupId}`
  TUIChatService.clearHistoryMessage(conversationID).then(() => {
    console.log('清除历史消息成功')
  })
}
