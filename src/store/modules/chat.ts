/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:33:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-29 15:34:26
 * @FilePath: /miaobi-admin-magic-touch/src/store/modules/chat.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import textToVideo from '@/assets/images/text-to-video.png'
import textToVideoActive from '@/assets/images/text-to-video-active.png'
import imageToVideo from '@/assets/images/image-to-video.png'
import imageToVideoActive from '@/assets/images/image-to-video-active.png'
import cankaoToVideo from '@/assets/images/cankao-to-video.png'
import cankaoToVideoActive from '@/assets/images/cankao-to-video-active.png'
import { defineStore } from 'pinia'

interface ToolItem {
  icon: string
  activeIcon: string
  title: string
  value: string
  imgLen?: number
  rule?: (value: unknown) => string
  tip?: string
}

// 功能JSON配置项的选择列表项
interface FunctionSelectOption {
  show_name: string
  is_default: string
  value: string
}

// 功能JSON配置项
interface FunctionJsonItem {
  text: string
  type: 'text' | 'select' | 'input'
  placeholder?: string
  list?: FunctionSelectOption[]
}

// 工具列表中的单个工具项
interface ToolListItem {
  functionName: string
  icon: string
  tips: string
  functionClassify: string
  functionTemplate: string
  functionJson: FunctionJsonItem[]
}

// 工具分类
interface ToolCategory {
  functionName?: string
  functionClassify: string
  toolList: ToolListItem[]
}

type MessageType = 'text' | 'image' | 'audio' | 'video' | 'other' | 'bigImage'

interface QuoteItem {
  messageID: string // 引用消息的id
  data: string // 引用消息的内容
  type: MessageType | string // 引用消息的类型
  userName: string // 引用消息的发送者
  cover: string // 引用消息的封面
}

interface IMessageModel {
  ID: string
}
interface ISpecialCourseConfig {
  icon?: string // 图标URL
  tips?: string // 提示文字
  functionName?: string // 功能名称，如 "/文生图"
  functionJson?: Array<{
    text: string // 文本内容
    type: 'text' | 'select' | 'input' // 类型：文本或选择器或输入框
    list?: Array<{
      show_name: string // 显示名称
      is_default: string // 是否默认选中，"true"/"false"
      value: string // 实际值
    }> // 当type为select时的选项列表
  }> // 功能配置的JSON数组
}
interface ChatState {
  groupId: string
  sendStatus: boolean
  showAiVideo: boolean
  showAiTool: boolean
  showAiToolBar: boolean
  chooseTool: string
  toolList: ToolItem[]
  radioMap: Record<string, Record<string, string>>
  quoteList: QuoteItem[]
  tools: ToolCategory[]
  fourTools: ToolListItem[]
  tool: string
  filterKeyword: string
  selectedMessages: IMessageModel[]
  videoControlsVisible: boolean
  videoPlaying: boolean
  fullscreen: boolean
  videoSrc: string
  videoPoster: string
  videoMessage: any
  fullscreenVideoTime: number
  fullscreenVideoPlaying: boolean
  isTyping: boolean
  aiCutoutsShow: boolean
  aiCutoutsSrc: string
  assistantShow: boolean
  mute: number
  assistantInputShow: boolean
  specialCourse: boolean
  specialCourseConfig: ISpecialCourseConfig
  selectTextShow: boolean
  selectMessage: any
  // ImageEditor 状态
  imageEditor: ImageEditorState
  // IMImagePreview 状态
  imagePreviewOpen: boolean
  currentPreviewId: string
  historyMessages: any[]
  sessionId: string
  messageList: any[]
  nextReqMessageID: string
}

const toolList: ToolItem[] = [
  {
    icon: textToVideo,
    activeIcon: textToVideoActive,
    title: '文生视频JM',
    value: 'text_2_video_hs',
    imgLen: 0,
    rule: (value: unknown) => '',
  },
  {
    icon: textToVideo,
    activeIcon: textToVideoActive,
    title: '文生视频VD',
    value: 'text_2_video',
    imgLen: 0,
    rule: (value: unknown) => '',
  },
  {
    icon: imageToVideo,
    activeIcon: imageToVideoActive,
    title: '图生视频JM',
    value: 'images_2_video_hs',
    imgLen: 1,
    rule: (value: unknown) => '',
  },
  {
    icon: imageToVideo,
    activeIcon: imageToVideoActive,
    title: '图生视频VD',
    value: 'images_2_video',
    imgLen: 1,
    rule: (value: unknown) => '',
  },
  {
    icon: cankaoToVideo,
    activeIcon: cankaoToVideoActive,
    title: '参考生视频VD',
    value: 'refer_2_video',
    imgLen: 3,
    rule: (value: unknown) => '',
    tip: '上传一个或多个人物/物体/场景作为参考主体最多上传3张图片',
  },
  {
    icon: cankaoToVideo,
    activeIcon: cankaoToVideoActive,
    title: '首尾帧视频VD',
    value: 'start_end_video',
    imgLen: 2,
    rule: (value: unknown) => {
      // if ((value as any).data.content.data.images.length < 2) return '请上传首尾帧图片'
      // else return ''
      return ''
    },
    tip: '上传首帧和尾帧图片作为参考，结合描述生成内容',
  },
]

const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    groupId: '',
    sendStatus: false, //是否可以发送或者编辑中
    showAiVideo: false, //是否显示AI视频
    showAiTool: false, //是否显示AI工具
    showAiToolBar: false, //是否显示AI工具bar
    chooseTool: 'text_2_video_hs', //选择的工具
    toolList: [
      {
        icon: textToVideo,
        activeIcon: textToVideoActive,
        title: '文生视频JM',
        value: 'text_2_video_hs',
      },
      {
        icon: textToVideo,
        activeIcon: textToVideoActive,
        title: '文生视频VD',
        value: 'text_2_video',
      },
      {
        icon: imageToVideo,
        activeIcon: imageToVideoActive,
        title: '图生视频JM',
        value: 'images_2_video_hs',
      },
      {
        icon: imageToVideo,
        activeIcon: imageToVideoActive,
        title: '图生视频VD',
        value: 'images_2_video',
      },
      {
        icon: cankaoToVideo,
        activeIcon: cankaoToVideoActive,
        title: '参考生视频VD',
        value: 'refer_2_video',
      },
    ],
    radioMap: {
      '16:9': {
        width: '410px',
        height: '231px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        overflow: 'hidden',
      },
      '4:3': {
        width: '400px',
        height: '300px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        overflow: 'hidden',
      },
      '1:1': {
        width: '350px',
        height: '350px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        overflow: 'hidden',
      },
      '3:4': {
        width: '300px',
        height: '400px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        overflow: 'hidden',
      },
      '9:16': {
        width: '231px',
        height: '410px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      },
      '21:9': {
        width: '630px',
        height: '270px',
        'border-radius': '10px',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        overflow: 'hidden',
      },
    },
    quoteList: [],
    tools: [],
    tool: '',
    filterKeyword: '',
    selectedMessages: [],
    videoControlsVisible: true,
    videoPlaying: false,
    fullscreen: false,
    videoSrc: '',
    videoPoster: '',
    videoMessage: {},
    fullscreenVideoTime: 0,
    fullscreenVideoPlaying: false,
    isTyping: false,
    aiCutoutsShow: false,
    aiCutoutsSrc: '',
    assistantShow: false,
    fourTools: [],
    // 触发静音
    mute: 0,
    assistantInputShow: false,
    specialCourse: false,
    specialCourseConfig: {},
    selectTextShow: false,
    selectMessage: null,
    // ImageEditor 状态初始化
    imageEditor: {
      visible: false,
      imageUrl: '',
      type: '',
      currentTool: 'brush',
      brushSize: 10,
      isLoading: false,
      showPreview: false,
      showToast: false,
      showNextStepModal: false,
      inputText: '',
      uploadUrl: '',
      drawingUrl: '', // 初始化为空
      canUndo: false,
      canRedo: false,
    },
    // IMImagePreview 状态初始化
    imagePreviewOpen: false,
    currentPreviewId: '',
    historyMessages: [],
    sessionId: '',
    messageList: [],
    nextReqMessageID: '0',
  }),
  actions: {
    setGroupId(groupId: string): void {
      this.groupId = groupId
    },
    setSendStatus(status: boolean): void {
      this.sendStatus = status
    },
    setShowAiVideo(status: boolean): void {
      this.showAiVideo = status
      if (!status) {
        this.chooseTool = toolList[0].value
      }
    },
    setShowAiTool(status: boolean): void {
      this.showAiTool = status
      if (!status) {
        this.filterKeyword = ''
      }
    },
    setShowAiToolBar(status: boolean): void {
      this.showAiToolBar = status
    },
    setChooseTool(tool: string): void {
      const foundTool = toolList.find(item => item.value === tool)
      this.chooseTool = foundTool ? foundTool.value : toolList[0].value
    },
    setQuoteList(list: QuoteItem[]): void {
      this.quoteList = list
    },
    addQuoteList(item: QuoteItem): void {
      this.quoteList.push(item)
    },
    removeQuoteList(item: QuoteItem): void {
      this.quoteList = this.quoteList.filter((quote: QuoteItem) => quote.messageID !== item.messageID)
    },
    setTools(list: ToolCategory[]): void {
      this.tools = list
    },
    setFourTools(list: ToolListItem[]): void {
      this.fourTools = list
    },
    setTool(tool: string): void {
      this.tool = tool
    },
    setFilterKeyword(keyword: string): void {
      this.filterKeyword = keyword
    },
    addSelectedMessage(message: IMessageModel): void {
      if (!this.selectedMessages.find(msg => msg.ID === message.ID)) {
        this.selectedMessages.push(message)
      }
    },
    removeSelectedMessage(messageId: string): void {
      this.selectedMessages = this.selectedMessages.filter(msg => msg.ID !== messageId)
    },
    clearSelectedMessages(): void {
      this.selectedMessages = []
    },
    getSelectedMessages(): IMessageModel[] {
      return this.selectedMessages
    },
    setVideoControlsVisible(visible: boolean): void {
      this.videoControlsVisible = visible
    },
    setVideoPlaying(playing: boolean): void {
      this.videoPlaying = playing
    },
    setFullscreen(fullscreen: boolean): void {
      this.fullscreen = fullscreen
    },
    setVideoSrc(src: string): void {
      this.videoSrc = src
    },
    setVideoPoster(poster: string): void {
      this.videoPoster = poster
    },
    setVideoMessage(message: any): void {
      this.videoMessage = message
    },
    setFullscreenVideoTime(time: number): void {
      this.fullscreenVideoTime = time
    },
    setFullscreenVideoPlaying(playing: boolean): void {
      this.fullscreenVideoPlaying = playing
    },
    setIsTyping(isTyping: boolean): void {
      this.isTyping = isTyping
    },
    setAiCutoutsShow(aiCutoutsShow: boolean) {
      this.aiCutoutsShow = aiCutoutsShow
    },
    setAiCutoutsSrc(aiCutoutsSrc: string) {
      this.aiCutoutsSrc = aiCutoutsSrc
    },
    setAssistantShow(assistantShow: boolean) {
      this.assistantShow = assistantShow
    },
    setAssistantInputShow(assistantInputShow: boolean) {
      this.assistantInputShow = assistantInputShow
    },
    setMute(mute: number) {
      this.mute = mute
    },
    setSpecialCourse(specialCourse: boolean) {
      this.specialCourse = specialCourse
    },
    setSpecialCourseConfig(specialCourseConfig: ISpecialCourseConfig) {
      this.specialCourseConfig = specialCourseConfig
    },
    setSelectTextShow(selectTextShow: boolean) {
      this.selectTextShow = selectTextShow
    },
    setSelectMessage(selectMessage: any) {
      this.selectMessage = selectMessage
    },
    // ImageEditor 相关 actions
    setImageEditorVisible(visible: boolean) {
      this.imageEditor.visible = visible
    },
    setImageEditorImageUrl(imageUrl: string) {
      this.imageEditor.imageUrl = imageUrl
    },
    setImageEditorType(type: string) {
      this.imageEditor.type = type
    },
    setImageEditorCurrentTool(tool: 'brush' | 'eraser') {
      this.imageEditor.currentTool = tool
    },
    setImageEditorBrushSize(size: number) {
      this.imageEditor.brushSize = size
    },
    setImageEditorLoading(loading: boolean) {
      this.imageEditor.isLoading = loading
    },
    setImageEditorShowPreview(show: boolean) {
      this.imageEditor.showPreview = show
    },
    setImageEditorShowToast(show: boolean) {
      this.imageEditor.showToast = show
    },
    setImageEditorShowNextStepModal(show: boolean) {
      this.imageEditor.showNextStepModal = show
    },
    setImageEditorInputText(text: string) {
      this.imageEditor.inputText = text
    },
    setImageEditorUploadUrl(url: string) {
      this.imageEditor.uploadUrl = url
    },
    setImageEditorDrawingUrl(url: string) {
      this.imageEditor.drawingUrl = url
    },
    setImageEditorCanUndo(canUndo: boolean) {
      this.imageEditor.canUndo = canUndo
    },
    setImageEditorCanRedo(canRedo: boolean) {
      this.imageEditor.canRedo = canRedo
    },
    // 打开ImageEditor的便捷方法
    openImageEditor(imageUrl: string, type: string) {
      this.imageEditor.imageUrl = imageUrl
      this.imageEditor.type = type
      this.imageEditor.visible = true
      // 重置其他状态
      this.imageEditor.currentTool = 'brush'
      this.imageEditor.brushSize = 10
      this.imageEditor.isLoading = false
      this.imageEditor.showPreview = false
      this.imageEditor.showToast = false
      this.imageEditor.showNextStepModal = false
      this.imageEditor.inputText = ''
      this.imageEditor.uploadUrl = ''
      this.imageEditor.drawingUrl = '' // 重置绘制URL
      this.imageEditor.canUndo = false
      this.imageEditor.canRedo = false
    },
    // 关闭ImageEditor的便捷方法
    closeImageEditor() {
      this.imageEditor.visible = false
      this.imageEditor.imageUrl = ''
      this.imageEditor.type = ''
    },

    // IMImagePreview 相关 actions
    setImagePreviewOpen(open: boolean) {
      this.imagePreviewOpen = open
    },
    setCurrentPreviewId(id: string) {
      this.currentPreviewId = id
    },
    // 关闭所有图片相关弹窗的便捷方法
    closeAllImageDialogs() {
      this.closeImageEditor()
      this.setImagePreviewOpen(false)
      this.setCurrentPreviewId('')
    },
    setHistoryMessages(messages: any[]) {
      this.historyMessages = [...messages, ...this.historyMessages]
    },
    setSessionId(sessionId: string) {
      this.sessionId = sessionId
    },
    setMessageList(messageList: any[]) {
      this.messageList = [...this.historyMessages, ...messageList]
    },
    setNextReqMessageID(nextReqMessageID: string) {
      this.nextReqMessageID = nextReqMessageID
    },
  },
})
export default useChatStore
